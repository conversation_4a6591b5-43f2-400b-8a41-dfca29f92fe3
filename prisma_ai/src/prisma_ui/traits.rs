// =================================================================================================
// File: /prisma_ai/src/prisma_ui/traits.rs
// =================================================================================================
// Purpose: Trait definitions and interfaces for UI component abstraction and polymorphism.
// This file defines the core traits that establish contracts for UI services, publishers,
// and other components, enabling modular design, testability, and flexible implementations
// across the prisma_ui module.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Core UI data types used in trait method signatures
// - publishers/types.rs: Publisher-specific types for publishing traits
// - services/types.rs: Service layer types for service trait definitions
// =================================================================================================
// External Dependencies:
// - async_trait: Enables async methods in traits for async service operations
// - std::future::Future: For async trait method return types
// - std::sync::Arc: For shared ownership in trait implementations
// - tokio::sync: For async synchronization primitives in traits
// =================================================================================================
// Module Interactions:
// - Implemented by service layer components for business logic abstraction
// - Used by publisher system for event distribution abstraction
// - Enables dependency injection and testing with mock implementations
// - Provides contracts for UI component lifecycle management
// - Facilitates integration with core PRISMA system traits
// =================================================================================================
// Trait Categories:
// - Service Traits: Abstractions for UI business logic and request handling
// - Publisher Traits: Interfaces for event publishing and communication
// - Configuration Traits: Abstractions for dynamic configuration management
// - Lifecycle Traits: Interfaces for component initialization and cleanup
// - Integration Traits: Contracts for bridging UI with backend systems
// =================================================================================================

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::err::types::PrismaResult;
use super::types::*;

// =================================================================================================
// Service Traits - Core abstractions for UI business logic and request handling
// =================================================================================================

/// Core trait for UI service lifecycle management
#[async_trait]
pub trait UiService: Send + Sync {
    /// Initialize the service with configuration
    async fn initialize(&mut self, config: &UiConfig) -> PrismaResult<()>;

    /// Start the service
    async fn start(&mut self) -> PrismaResult<()>;

    /// Stop the service gracefully
    async fn stop(&mut self) -> PrismaResult<()>;

    /// Pause the service (temporary stop)
    async fn pause(&mut self) -> PrismaResult<()>;

    /// Resume the service from paused state
    async fn resume(&mut self) -> PrismaResult<()>;

    /// Get current service status
    fn status(&self) -> ServiceStatus;

    /// Get service health information
    async fn health_check(&self) -> PrismaResult<ServiceHealth>;

    /// Get service name for identification
    fn service_name(&self) -> &str;
}

/// Service status enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ServiceStatus {
    /// Service is not initialized
    NotInitialized,
    /// Service is initializing
    Initializing,
    /// Service is running normally
    Running,
    /// Service is paused
    Paused,
    /// Service is stopping
    Stopping,
    /// Service is stopped
    Stopped,
    /// Service encountered an error
    Error(String),
}

/// Trait for request handling services
#[async_trait]
pub trait RequestHandler<TRequest, TResponse>: Send + Sync {
    /// Handle a request and return a response
    async fn handle_request(&self, request: UiRequest<TRequest>) -> PrismaResult<UiResponse<TResponse>>;

    /// Validate a request before processing
    async fn validate_request(&self, request: &UiRequest<TRequest>) -> PrismaResult<()>;

    /// Get supported request types
    fn supported_request_types(&self) -> Vec<String>;
}

/// Trait for session management
#[async_trait]
pub trait SessionManager: Send + Sync {
    /// Create a new user session
    async fn create_session(&self, user_id: UserId, device_info: DeviceInfo) -> PrismaResult<UserSession>;

    /// Validate an existing session
    async fn validate_session(&self, session_id: &SessionId) -> PrismaResult<UserSession>;

    /// Update session activity
    async fn update_activity(&self, session_id: &SessionId) -> PrismaResult<()>;

    /// Terminate a session
    async fn terminate_session(&self, session_id: &SessionId) -> PrismaResult<()>;

    /// Get all active sessions for a user
    async fn get_user_sessions(&self, user_id: &UserId) -> PrismaResult<Vec<UserSession>>;

    /// Clean up expired sessions
    async fn cleanup_expired_sessions(&self) -> PrismaResult<u32>; // Returns count of cleaned sessions
}

/// Trait for authentication services
#[async_trait]
pub trait AuthenticationService: Send + Sync {
    /// Authenticate user with email and password
    async fn authenticate(&self, email: &str, password: &str) -> PrismaResult<AuthenticationResult>;

    /// Refresh an authentication token
    async fn refresh_token(&self, refresh_token: &str) -> PrismaResult<AuthenticationResult>;

    /// Validate a JWT token
    async fn validate_token(&self, token: &str) -> PrismaResult<TokenClaims>;

    /// Register a new user
    async fn register_user(&self, registration: UserRegistration) -> PrismaResult<UserId>;

    /// Change user password
    async fn change_password(&self, user_id: &UserId, old_password: &str, new_password: &str) -> PrismaResult<()>;

    /// Reset user password
    async fn reset_password(&self, email: &str) -> PrismaResult<()>;
}

/// Authentication result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthenticationResult {
    pub user_id: UserId,
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: u64, // seconds
    pub token_type: String,
    pub permissions: Vec<Permission>,
}

/// JWT token claims
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenClaims {
    pub user_id: UserId,
    pub session_id: SessionId,
    pub permissions: Vec<Permission>,
    pub issued_at: i64,
    pub expires_at: i64,
}

/// User registration information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserRegistration {
    pub email: String,
    pub password: String,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub device_info: DeviceInfo,
}

// =================================================================================================
// Publisher Traits - Interfaces for event publishing and communication
// =================================================================================================

/// Core trait for event publishing to UI clients
#[async_trait]
pub trait EventPublisher: Send + Sync {
    /// Publish an event to all connected clients
    async fn publish_event(&self, event: &UiEvent) -> PrismaResult<()>;

    /// Publish an event to a specific session
    async fn publish_to_session(&self, session_id: &SessionId, event: &UiEvent) -> PrismaResult<()>;

    /// Publish an event to a specific user (all their sessions)
    async fn publish_to_user(&self, user_id: &UserId, event: &UiEvent) -> PrismaResult<()>;

    /// Broadcast a system notification to all clients
    async fn broadcast_notification(&self, notification: &SystemNotificationData) -> PrismaResult<()>;

    /// Get publisher health status
    async fn health_check(&self) -> PrismaResult<PublisherHealth>;
}

/// Health information for publishers
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PublisherHealth {
    pub publisher_name: String,
    pub status: ConnectionStatus,
    pub connected_clients: u32,
    pub message_queue_size: u32,
    pub last_message_sent: Option<chrono::DateTime<chrono::Utc>>,
    pub error_count: u32,
}

/// Trait for WebSocket message handling
#[async_trait]
pub trait WebSocketHandler: Send + Sync {
    /// Handle incoming WebSocket message
    async fn handle_message(&self, session_id: &SessionId, message: &WebSocketMessage) -> PrismaResult<()>;

    /// Handle client connection
    async fn on_connect(&self, session_id: &SessionId, user_id: &UserId) -> PrismaResult<()>;

    /// Handle client disconnection
    async fn on_disconnect(&self, session_id: &SessionId) -> PrismaResult<()>;

    /// Send message to specific client
    async fn send_to_client(&self, session_id: &SessionId, message: &WebSocketMessage) -> PrismaResult<()>;

    /// Get connected clients count
    fn connected_clients_count(&self) -> u32;
}

/// Trait for RabbitMQ message publishing
#[async_trait]
pub trait RabbitMqPublisher: Send + Sync {
    /// Publish message to RabbitMQ exchange
    async fn publish(&self, routing_key: &str, message: &[u8]) -> PrismaResult<()>;

    /// Publish with custom properties
    async fn publish_with_properties(
        &self,
        routing_key: &str,
        message: &[u8],
        properties: MessageProperties
    ) -> PrismaResult<()>;

    /// Declare exchange if it doesn't exist
    async fn declare_exchange(&self, exchange_name: &str, exchange_type: ExchangeType) -> PrismaResult<()>;

    /// Get connection status
    fn connection_status(&self) -> ConnectionStatus;
}

/// Message properties for RabbitMQ publishing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageProperties {
    pub content_type: Option<String>,
    pub content_encoding: Option<String>,
    pub correlation_id: Option<String>,
    pub reply_to: Option<String>,
    pub expiration: Option<String>,
    pub message_id: Option<String>,
    pub timestamp: Option<chrono::DateTime<chrono::Utc>>,
    pub message_type: Option<String>,
    pub user_id: Option<String>,
    pub app_id: Option<String>,
    pub priority: Option<u8>,
    pub delivery_mode: Option<u8>, // 1 = non-persistent, 2 = persistent
}

/// RabbitMQ exchange types
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ExchangeType {
    Direct,
    Fanout,
    Topic,
    Headers,
}

/// Trait for streaming data to UI clients
#[async_trait]
pub trait StreamingPublisher: Send + Sync {
    /// Start a streaming session
    async fn start_stream(&self, session_id: &SessionId, stream_id: &str) -> PrismaResult<()>;

    /// Send streaming data chunk
    async fn send_chunk(&self, session_id: &SessionId, stream_id: &str, chunk: &[u8]) -> PrismaResult<()>;

    /// End a streaming session
    async fn end_stream(&self, session_id: &SessionId, stream_id: &str) -> PrismaResult<()>;

    /// Handle streaming error
    async fn handle_stream_error(&self, session_id: &SessionId, stream_id: &str, error: &str) -> PrismaResult<()>;

    /// Get active streams count
    fn active_streams_count(&self) -> u32;
}

// =================================================================================================
// Configuration Traits - Abstractions for dynamic configuration management
// =================================================================================================

/// Trait for configuration management
#[async_trait]
pub trait ConfigurationManager: Send + Sync {
    /// Load configuration from file
    async fn load_config(&self, config_path: &str) -> PrismaResult<UiConfig>;

    /// Save configuration to file
    async fn save_config(&self, config: &UiConfig, config_path: &str) -> PrismaResult<()>;

    /// Reload configuration from file
    async fn reload_config(&self) -> PrismaResult<UiConfig>;

    /// Get current configuration
    fn get_config(&self) -> &UiConfig;

    /// Update configuration section
    async fn update_config_section(&mut self, section: ConfigSection) -> PrismaResult<()>;

    /// Validate configuration
    async fn validate_config(&self, config: &UiConfig) -> PrismaResult<Vec<ConfigValidationError>>;

    /// Watch for configuration file changes
    async fn watch_config_changes(&self) -> PrismaResult<tokio::sync::mpsc::Receiver<ConfigChangeEvent>>;
}

/// Configuration sections that can be updated
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "section_type", content = "data")]
pub enum ConfigSection {
    Server(ServerConfig),
    RabbitMq(RabbitMqConfig),
    Database(DatabaseConfig),
    Auth(AuthConfig),
    Features(FeatureConfig),
    UiSettings(UiSettings),
}

/// Configuration validation error
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigValidationError {
    pub section: String,
    pub field: String,
    pub error_type: ValidationErrorType,
    pub message: String,
}

/// Types of configuration validation errors
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ValidationErrorType {
    Required,
    InvalidFormat,
    OutOfRange,
    InvalidValue,
    Conflict,
}

/// Configuration change event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigChangeEvent {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub change_type: ConfigChangeType,
    pub section: String,
    pub old_value: Option<serde_json::Value>,
    pub new_value: Option<serde_json::Value>,
}

/// Types of configuration changes
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ConfigChangeType {
    Added,
    Modified,
    Removed,
    Reloaded,
}

/// Trait for environment-specific configuration
#[async_trait]
pub trait EnvironmentConfig: Send + Sync {
    /// Get environment name (development, staging, production)
    fn environment(&self) -> &str;

    /// Check if running in development mode
    fn is_development(&self) -> bool;

    /// Check if running in production mode
    fn is_production(&self) -> bool;

    /// Get environment-specific configuration overrides
    async fn get_environment_overrides(&self) -> PrismaResult<HashMap<String, serde_json::Value>>;

    /// Apply environment-specific settings
    async fn apply_environment_settings(&self, config: &mut UiConfig) -> PrismaResult<()>;
}

// =================================================================================================
// Lifecycle Traits - Interfaces for component initialization and cleanup
// =================================================================================================

/// Trait for component lifecycle management
#[async_trait]
pub trait Lifecycle: Send + Sync {
    /// Initialize the component
    async fn initialize(&mut self) -> PrismaResult<()>;

    /// Start the component
    async fn start(&mut self) -> PrismaResult<()>;

    /// Stop the component gracefully
    async fn stop(&mut self) -> PrismaResult<()>;

    /// Restart the component
    async fn restart(&mut self) -> PrismaResult<()> {
        self.stop().await?;
        self.start().await
    }

    /// Get component status
    fn status(&self) -> ComponentStatus;

    /// Get component name
    fn name(&self) -> &str;
}

/// Component status
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ComponentStatus {
    NotInitialized,
    Initializing,
    Ready,
    Starting,
    Running,
    Stopping,
    Stopped,
    Error(String),
}

/// Trait for health monitoring
#[async_trait]
pub trait HealthMonitor: Send + Sync {
    /// Perform health check
    async fn health_check(&self) -> PrismaResult<HealthStatus>;

    /// Get detailed health report
    async fn detailed_health_report(&self) -> PrismaResult<DetailedHealthReport>;

    /// Register health check callback
    async fn register_health_callback(&self, callback: Box<dyn HealthCallback>) -> PrismaResult<()>;

    /// Set health check interval
    async fn set_health_check_interval(&mut self, interval: std::time::Duration) -> PrismaResult<()>;
}

/// Health status
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum HealthStatus {
    Healthy,
    Degraded,
    Unhealthy,
    Unknown,
}

/// Detailed health report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetailedHealthReport {
    pub component_name: String,
    pub status: HealthStatus,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub checks: Vec<HealthCheck>,
    pub metrics: HashMap<String, f64>,
    pub dependencies: Vec<DependencyHealth>,
}

/// Individual health check
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheck {
    pub name: String,
    pub status: HealthStatus,
    pub message: Option<String>,
    pub duration_ms: u64,
}

/// Dependency health information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyHealth {
    pub name: String,
    pub status: HealthStatus,
    pub last_check: chrono::DateTime<chrono::Utc>,
    pub error_message: Option<String>,
}

/// Health check callback trait
#[async_trait]
pub trait HealthCallback: Send + Sync {
    /// Called when health status changes
    async fn on_health_change(&self, old_status: HealthStatus, new_status: HealthStatus) -> PrismaResult<()>;

    /// Called when health check fails
    async fn on_health_check_failure(&self, error: &str) -> PrismaResult<()>;
}

// =================================================================================================
// Integration Traits - Contracts for bridging UI with backend systems
// =================================================================================================

/// Trait for PrismaEngine integration
#[async_trait]
pub trait PrismaEngineIntegration: Send + Sync {
    /// Submit a task to the PrismaEngine
    async fn submit_task(&self, request: TaskSubmissionRequest) -> PrismaResult<TaskSubmissionResponse>;

    /// Get task status
    async fn get_task_status(&self, task_id: &str) -> PrismaResult<TaskStatusResponse>;

    /// Cancel a running task
    async fn cancel_task(&self, task_id: &str) -> PrismaResult<()>;

    /// Get engine status
    async fn get_engine_status(&self) -> PrismaResult<EngineStatusResponse>;

    /// Subscribe to task progress updates
    async fn subscribe_to_task_progress(&self, task_id: &str) -> PrismaResult<tokio::sync::mpsc::Receiver<TaskProgressData>>;
}

/// Task submission response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskSubmissionResponse {
    pub task_id: String,
    pub status: TaskStatus,
    pub estimated_completion: Option<chrono::DateTime<chrono::Utc>>,
    pub queue_position: Option<u32>,
}

/// Task status response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskStatusResponse {
    pub task_id: String,
    pub status: TaskStatus,
    pub progress: Option<TaskProgress>,
    pub result: Option<serde_json::Value>,
    pub error: Option<String>,
    pub started_at: Option<chrono::DateTime<chrono::Utc>>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// Engine status response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineStatusResponse {
    pub status: ServiceStatus,
    pub active_tasks: u32,
    pub queued_tasks: u32,
    pub completed_tasks: u64,
    pub failed_tasks: u64,
    pub uptime_seconds: u64,
    pub resource_usage: ResourceUsage,
}

/// Resource usage information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUsage {
    pub cpu_usage_percent: f32,
    pub memory_usage_mb: u64,
    pub gpu_usage_percent: Option<f32>,
    pub disk_usage_mb: u64,
}

/// Trait for Agent Manager integration
#[async_trait]
pub trait AgentManagerIntegration: Send + Sync {
    /// Register a new agent
    async fn register_agent(&self, request: AgentRegistrationRequest) -> PrismaResult<AgentRegistrationResponse>;

    /// Get agent status
    async fn get_agent_status(&self, agent_id: &AgentId) -> PrismaResult<AgentStatusResponse>;

    /// Update agent configuration
    async fn update_agent_config(&self, agent_id: &AgentId, config: AgentConfiguration) -> PrismaResult<()>;

    /// Start an agent
    async fn start_agent(&self, agent_id: &AgentId) -> PrismaResult<()>;

    /// Stop an agent
    async fn stop_agent(&self, agent_id: &AgentId) -> PrismaResult<()>;

    /// List all agents
    async fn list_agents(&self) -> PrismaResult<Vec<AgentInfo>>;

    /// Subscribe to agent status updates
    async fn subscribe_to_agent_updates(&self, agent_id: &AgentId) -> PrismaResult<tokio::sync::mpsc::Receiver<AgentStatusData>>;
}

/// Agent registration response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentRegistrationResponse {
    pub agent_id: AgentId,
    pub sequence_id: u64,
    pub status: AgentStatus,
    pub capabilities: Vec<String>,
}

/// Agent status response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentStatusResponse {
    pub agent_id: AgentId,
    pub status: AgentStatus,
    pub current_task: Option<String>,
    pub capabilities: Vec<String>,
    pub configuration: AgentConfiguration,
    pub statistics: AgentStatistics,
}

/// Agent information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentInfo {
    pub agent_id: AgentId,
    pub name: String,
    pub status: AgentStatus,
    pub capabilities: Vec<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_activity: Option<chrono::DateTime<chrono::Utc>>,
}

/// Agent statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentStatistics {
    pub tasks_completed: u64,
    pub tasks_failed: u64,
    pub average_response_time_ms: f64,
    pub uptime_seconds: u64,
    pub total_tokens_processed: u64,
}

/// Trait for Storage integration
#[async_trait]
pub trait StorageIntegration: Send + Sync {
    /// Execute a storage operation
    async fn execute_operation(&self, request: StorageRequest) -> PrismaResult<StorageResponse>;

    /// Get conversation history
    async fn get_conversation_history(&self, conversation_id: &ConversationId, limit: Option<u32>) -> PrismaResult<Vec<ChatMessageData>>;

    /// Save conversation message
    async fn save_conversation_message(&self, message: &ChatMessageData) -> PrismaResult<()>;

    /// Get user preferences
    async fn get_user_preferences(&self, user_id: &UserId) -> PrismaResult<UserPreferences>;

    /// Save user preferences
    async fn save_user_preferences(&self, user_id: &UserId, preferences: &UserPreferences) -> PrismaResult<()>;

    /// Get storage health
    async fn get_storage_health(&self) -> PrismaResult<StorageHealth>;
}

/// Storage operation response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageResponse {
    pub operation: StorageOperation,
    pub success: bool,
    pub data: Option<serde_json::Value>,
    pub affected_rows: Option<u32>,
    pub error: Option<String>,
}

/// User preferences
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPreferences {
    pub theme: String,
    pub language: String,
    pub timezone: String,
    pub notification_settings: NotificationSettings,
    pub ui_settings: HashMap<String, serde_json::Value>,
}

/// Storage health information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageHealth {
    pub status: ConnectionStatus,
    pub connection_pool_size: u32,
    pub active_connections: u32,
    pub query_response_time_ms: f64,
    pub storage_usage_mb: u64,
    pub last_backup: Option<chrono::DateTime<chrono::Utc>>,
}

// =================================================================================================
// Default Implementations
// =================================================================================================

impl Default for ServiceStatus {
    fn default() -> Self {
        ServiceStatus::NotInitialized
    }
}

impl Default for ComponentStatus {
    fn default() -> Self {
        ComponentStatus::NotInitialized
    }
}

impl Default for HealthStatus {
    fn default() -> Self {
        HealthStatus::Unknown
    }
}

impl Default for ExchangeType {
    fn default() -> Self {
        ExchangeType::Direct
    }
}

impl Default for ValidationErrorType {
    fn default() -> Self {
        ValidationErrorType::InvalidValue
    }
}

impl Default for ConfigChangeType {
    fn default() -> Self {
        ConfigChangeType::Modified
    }
}