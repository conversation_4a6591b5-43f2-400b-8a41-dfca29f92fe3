// =================================================================================================
// File: /prisma_ai/src/prisma_ui/generics.rs
// =================================================================================================
// Purpose: Generic implementations and utility functions for UI component reusability.
// This file provides generic implementations of common UI patterns, utility functions,
// and reusable components that can be parameterized for different UI operations while
// maintaining type safety and reducing code duplication across the prisma_ui module.
// =================================================================================================
// Internal Dependencies:
// - traits.rs: UI trait definitions for generic implementations
// - types.rs: Core UI data types used in generic functions
// - publishers/types.rs: Publisher types for generic publishing utilities
// - services/types.rs: Service types for generic service implementations
// =================================================================================================
// External Dependencies:
// - std::marker::PhantomData: For zero-cost generic type parameters
// - std::collections: Generic collection utilities and operations
// - serde: Generic serialization and deserialization utilities
// - async_trait: For generic async trait implementations
// - tokio: Async utilities for generic concurrent operations
// =================================================================================================
// Module Interactions:
// - Provides reusable implementations for service layer components
// - Offers generic utilities for publisher system operations
// - Enables parameterized UI component creation and management
// - Facilitates type-safe generic operations across UI modules
// - Supports generic integration patterns with backend systems
// =================================================================================================
// Generic Categories:
// - Service Generics: Parameterized service implementations and utilities
// - Publisher Generics: Generic event publishing and communication utilities
// - Configuration Generics: Type-safe configuration management utilities
// - Collection Generics: Generic operations on UI data collections
// - Integration Generics: Parameterized backend integration utilities
// =================================================================================================

use async_trait::async_trait;
use serde::{Deserialize, Serialize, de::DeserializeOwned};
use std::collections::HashMap;
use std::marker::PhantomData;
use std::sync::Arc;
use tokio::sync::{RwLock, Mutex};
use crate::err::types::PrismaResult;
use super::types::*;
use super::traits::*;

// =================================================================================================
// Generic Service Patterns - Parameterized service implementations and utilities
// =================================================================================================

/// Generic service wrapper that provides common functionality
pub struct GenericService<T> {
    inner: Arc<RwLock<T>>,
    status: Arc<RwLock<ServiceStatus>>,
    name: String,
    config: Arc<RwLock<Option<UiConfig>>>,
}

impl<T> GenericService<T>
where
    T: Send + Sync + 'static,
{
    /// Create a new generic service wrapper
    pub fn new(inner: T, name: impl Into<String>) -> Self {
        Self {
            inner: Arc::new(RwLock::new(inner)),
            status: Arc::new(RwLock::new(ServiceStatus::NotInitialized)),
            name: name.into(),
            config: Arc::new(RwLock::new(None)),
        }
    }

    /// Get a read lock on the inner service
    pub async fn read(&self) -> tokio::sync::RwLockReadGuard<'_, T> {
        self.inner.read().await
    }

    /// Get a write lock on the inner service
    pub async fn write(&self) -> tokio::sync::RwLockWriteGuard<'_, T> {
        self.inner.write().await
    }

    /// Get current status
    pub async fn get_status(&self) -> ServiceStatus {
        self.status.read().await.clone()
    }

    /// Set status
    pub async fn set_status(&self, status: ServiceStatus) {
        *self.status.write().await = status;
    }

    /// Get service name
    pub fn get_name(&self) -> &str {
        &self.name
    }

    /// Get configuration
    pub async fn get_config(&self) -> Option<UiConfig> {
        self.config.read().await.clone()
    }

    /// Set configuration
    pub async fn set_config(&self, config: UiConfig) {
        *self.config.write().await = Some(config);
    }
}

/// Generic request/response handler
pub struct GenericRequestHandler<TRequest, TResponse, TService> {
    service: Arc<TService>,
    validator: Option<Box<dyn RequestValidator<TRequest> + Send + Sync>>,
    transformer: Option<Box<dyn ResponseTransformer<TResponse> + Send + Sync>>,
    _phantom: PhantomData<(TRequest, TResponse)>,
}

impl<TRequest, TResponse, TService> GenericRequestHandler<TRequest, TResponse, TService>
where
    TRequest: Send + Sync + 'static,
    TResponse: Send + Sync + 'static,
    TService: Send + Sync + 'static,
{
    /// Create a new generic request handler
    pub fn new(service: Arc<TService>) -> Self {
        Self {
            service,
            validator: None,
            transformer: None,
            _phantom: PhantomData,
        }
    }

    /// Add request validator
    pub fn with_validator(mut self, validator: Box<dyn RequestValidator<TRequest> + Send + Sync>) -> Self {
        self.validator = Some(validator);
        self
    }

    /// Add response transformer
    pub fn with_transformer(mut self, transformer: Box<dyn ResponseTransformer<TResponse> + Send + Sync>) -> Self {
        self.transformer = Some(transformer);
        self
    }

    /// Get service reference
    pub fn service(&self) -> &Arc<TService> {
        &self.service
    }
}

/// Trait for request validation
#[async_trait]
pub trait RequestValidator<T>: Send + Sync {
    async fn validate(&self, request: &UiRequest<T>) -> PrismaResult<()>;
}

/// Trait for response transformation
#[async_trait]
pub trait ResponseTransformer<T>: Send + Sync {
    async fn transform(&self, response: UiResponse<T>) -> PrismaResult<UiResponse<T>>;
}

/// Generic session manager implementation
pub struct GenericSessionManager<TStorage> {
    storage: Arc<TStorage>,
    config: AuthConfig,
    active_sessions: Arc<RwLock<HashMap<SessionId, UserSession>>>,
}

impl<TStorage> GenericSessionManager<TStorage>
where
    TStorage: Send + Sync + 'static,
{
    /// Create a new generic session manager
    pub fn new(storage: Arc<TStorage>, config: AuthConfig) -> Self {
        Self {
            storage,
            config,
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Get active sessions count
    pub async fn active_sessions_count(&self) -> usize {
        self.active_sessions.read().await.len()
    }

    /// Get all active sessions
    pub async fn get_active_sessions(&self) -> Vec<UserSession> {
        self.active_sessions.read().await.values().cloned().collect()
    }

    /// Add session to active sessions
    pub async fn add_active_session(&self, session: UserSession) {
        self.active_sessions.write().await.insert(session.session_id.clone(), session);
    }

    /// Remove session from active sessions
    pub async fn remove_active_session(&self, session_id: &SessionId) -> Option<UserSession> {
        self.active_sessions.write().await.remove(session_id)
    }
}

// =================================================================================================
// Publisher Generics - Generic event publishing and communication utilities
// =================================================================================================

/// Generic event publisher that can publish to multiple channels
pub struct GenericEventPublisher<TChannel> {
    channels: Arc<RwLock<HashMap<String, Arc<TChannel>>>>,
    default_channel: Option<String>,
    event_queue: Arc<Mutex<Vec<QueuedEvent>>>,
    max_queue_size: usize,
}

impl<TChannel> GenericEventPublisher<TChannel>
where
    TChannel: Send + Sync + 'static,
{
    /// Create a new generic event publisher
    pub fn new(max_queue_size: usize) -> Self {
        Self {
            channels: Arc::new(RwLock::new(HashMap::new())),
            default_channel: None,
            event_queue: Arc::new(Mutex::new(Vec::new())),
            max_queue_size,
        }
    }

    /// Add a publishing channel
    pub async fn add_channel(&self, name: impl Into<String>, channel: Arc<TChannel>) {
        let name = name.into();
        self.channels.write().await.insert(name, channel);
    }

    /// Remove a publishing channel
    pub async fn remove_channel(&self, name: &str) -> Option<Arc<TChannel>> {
        self.channels.write().await.remove(name)
    }

    /// Set default channel
    pub fn set_default_channel(&mut self, name: impl Into<String>) {
        self.default_channel = Some(name.into());
    }

    /// Get channel by name
    pub async fn get_channel(&self, name: &str) -> Option<Arc<TChannel>> {
        self.channels.read().await.get(name).cloned()
    }

    /// Queue an event for publishing
    pub async fn queue_event(&self, event: QueuedEvent) -> PrismaResult<()> {
        let mut queue = self.event_queue.lock().await;
        if queue.len() >= self.max_queue_size {
            queue.remove(0); // Remove oldest event
        }
        queue.push(event);
        Ok(())
    }

    /// Get queued events count
    pub async fn queued_events_count(&self) -> usize {
        self.event_queue.lock().await.len()
    }

    /// Drain all queued events
    pub async fn drain_queued_events(&self) -> Vec<QueuedEvent> {
        self.event_queue.lock().await.drain(..).collect()
    }
}

/// Queued event for deferred publishing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueuedEvent {
    pub event: UiEvent,
    pub target: EventTarget,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub retry_count: u32,
    pub max_retries: u32,
}

/// Event publishing target
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EventTarget {
    /// Broadcast to all connected clients
    Broadcast,
    /// Send to specific session
    Session(SessionId),
    /// Send to specific user (all their sessions)
    User(UserId),
    /// Send to specific channel
    Channel(String),
}

// =================================================================================================
// Configuration Generics - Type-safe configuration management utilities
// =================================================================================================

/// Generic configuration manager that can handle any configuration type
pub struct GenericConfigManager<T> {
    config: Arc<RwLock<T>>,
    file_path: String,
    watchers: Arc<RwLock<Vec<Arc<dyn ConfigWatcher<T> + Send + Sync>>>>,
    validation_rules: Arc<RwLock<Vec<Arc<dyn ConfigValidator<T> + Send + Sync>>>>,
}

impl<T> GenericConfigManager<T>
where
    T: Clone + Send + Sync + Serialize + DeserializeOwned + 'static,
{
    /// Create a new generic configuration manager
    pub fn new(initial_config: T, file_path: impl Into<String>) -> Self {
        Self {
            config: Arc::new(RwLock::new(initial_config)),
            file_path: file_path.into(),
            watchers: Arc::new(RwLock::new(Vec::new())),
            validation_rules: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Get current configuration
    pub async fn get_config(&self) -> T {
        self.config.read().await.clone()
    }

    /// Update configuration
    pub async fn update_config(&self, new_config: T) -> PrismaResult<()> {
        // Validate new configuration
        self.validate_config(&new_config).await?;

        let old_config = self.config.read().await.clone();
        *self.config.write().await = new_config.clone();

        // Notify watchers
        self.notify_watchers(&old_config, &new_config).await;

        Ok(())
    }

    /// Add configuration watcher
    pub async fn add_watcher(&self, watcher: Arc<dyn ConfigWatcher<T> + Send + Sync>) {
        self.watchers.write().await.push(watcher);
    }

    /// Add validation rule
    pub async fn add_validation_rule(&self, rule: Arc<dyn ConfigValidator<T> + Send + Sync>) {
        self.validation_rules.write().await.push(rule);
    }

    /// Validate configuration against all rules
    pub async fn validate_config(&self, config: &T) -> PrismaResult<()> {
        let rules = self.validation_rules.read().await;
        for rule in rules.iter() {
            rule.validate(config).await?;
        }
        Ok(())
    }

    /// Notify all watchers of configuration change
    async fn notify_watchers(&self, old_config: &T, new_config: &T) {
        let watchers = self.watchers.read().await;
        for watcher in watchers.iter() {
            if let Err(e) = watcher.on_config_change(old_config, new_config).await {
                tracing::error!("Configuration watcher error: {}", e);
            }
        }
    }

    /// Get file path
    pub fn file_path(&self) -> &str {
        &self.file_path
    }
}

/// Trait for configuration change watchers
#[async_trait]
pub trait ConfigWatcher<T>: Send + Sync {
    async fn on_config_change(&self, old_config: &T, new_config: &T) -> PrismaResult<()>;
}

/// Trait for configuration validators
#[async_trait]
pub trait ConfigValidator<T>: Send + Sync {
    async fn validate(&self, config: &T) -> PrismaResult<()>;
}

/// Generic configuration section manager
pub struct ConfigSectionManager<T> {
    sections: Arc<RwLock<HashMap<String, T>>>,
    default_section: Option<String>,
}

impl<T> ConfigSectionManager<T>
where
    T: Clone + Send + Sync + 'static,
{
    /// Create a new configuration section manager
    pub fn new() -> Self {
        Self {
            sections: Arc::new(RwLock::new(HashMap::new())),
            default_section: None,
        }
    }

    /// Add a configuration section
    pub async fn add_section(&self, name: impl Into<String>, config: T) {
        self.sections.write().await.insert(name.into(), config);
    }

    /// Get a configuration section
    pub async fn get_section(&self, name: &str) -> Option<T> {
        self.sections.read().await.get(name).cloned()
    }

    /// Update a configuration section
    pub async fn update_section(&self, name: &str, config: T) -> bool {
        if let Some(existing) = self.sections.write().await.get_mut(name) {
            *existing = config;
            true
        } else {
            false
        }
    }

    /// Remove a configuration section
    pub async fn remove_section(&self, name: &str) -> Option<T> {
        self.sections.write().await.remove(name)
    }

    /// List all section names
    pub async fn list_sections(&self) -> Vec<String> {
        self.sections.read().await.keys().cloned().collect()
    }

    /// Set default section
    pub fn set_default_section(&mut self, name: impl Into<String>) {
        self.default_section = Some(name.into());
    }

    /// Get default section
    pub async fn get_default_section(&self) -> Option<T> {
        if let Some(ref default_name) = self.default_section {
            self.get_section(default_name).await
        } else {
            None
        }
    }
}

// =================================================================================================
// Collection Generics - Generic operations on UI data collections
// =================================================================================================

/// Generic paginated collection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedCollection<T> {
    pub items: Vec<T>,
    pub total_count: u64,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
    pub has_next: bool,
    pub has_previous: bool,
}

impl<T> PaginatedCollection<T> {
    /// Create a new paginated collection
    pub fn new(items: Vec<T>, total_count: u64, page: u32, page_size: u32) -> Self {
        let total_pages = ((total_count as f64) / (page_size as f64)).ceil() as u32;
        let has_next = page < total_pages;
        let has_previous = page > 1;

        Self {
            items,
            total_count,
            page,
            page_size,
            total_pages,
            has_next,
            has_previous,
        }
    }

    /// Get items count in current page
    pub fn items_count(&self) -> usize {
        self.items.len()
    }

    /// Check if collection is empty
    pub fn is_empty(&self) -> bool {
        self.items.is_empty()
    }

    /// Map items to a different type
    pub fn map<U, F>(self, f: F) -> PaginatedCollection<U>
    where
        F: FnMut(T) -> U,
    {
        PaginatedCollection {
            items: self.items.into_iter().map(f).collect(),
            total_count: self.total_count,
            page: self.page,
            page_size: self.page_size,
            total_pages: self.total_pages,
            has_next: self.has_next,
            has_previous: self.has_previous,
        }
    }
}

/// Generic cache with TTL support
pub struct GenericCache<K, V> {
    data: Arc<RwLock<HashMap<K, CacheEntry<V>>>>,
    default_ttl: std::time::Duration,
    max_size: Option<usize>,
}

impl<K, V> GenericCache<K, V>
where
    K: Clone + Eq + std::hash::Hash + Send + Sync + 'static,
    V: Clone + Send + Sync + 'static,
{
    /// Create a new generic cache
    pub fn new(default_ttl: std::time::Duration, max_size: Option<usize>) -> Self {
        Self {
            data: Arc::new(RwLock::new(HashMap::new())),
            default_ttl,
            max_size,
        }
    }

    /// Insert an item into the cache
    pub async fn insert(&self, key: K, value: V) {
        self.insert_with_ttl(key, value, self.default_ttl).await;
    }

    /// Insert an item with custom TTL
    pub async fn insert_with_ttl(&self, key: K, value: V, ttl: std::time::Duration) {
        let mut data = self.data.write().await;

        // Check if we need to evict items due to size limit
        if let Some(max_size) = self.max_size {
            if data.len() >= max_size {
                // Remove oldest entry (simple LRU)
                if let Some(oldest_key) = data.keys().next().cloned() {
                    data.remove(&oldest_key);
                }
            }
        }

        let entry = CacheEntry {
            value,
            expires_at: std::time::Instant::now() + ttl,
        };
        data.insert(key, entry);
    }

    /// Get an item from the cache
    pub async fn get(&self, key: &K) -> Option<V> {
        let mut data = self.data.write().await;

        if let Some(entry) = data.get(key) {
            if entry.expires_at > std::time::Instant::now() {
                Some(entry.value.clone())
            } else {
                // Entry expired, remove it
                data.remove(key);
                None
            }
        } else {
            None
        }
    }

    /// Remove an item from the cache
    pub async fn remove(&self, key: &K) -> Option<V> {
        self.data.write().await.remove(key).map(|entry| entry.value)
    }

    /// Clear all items from the cache
    pub async fn clear(&self) {
        self.data.write().await.clear();
    }

    /// Get cache size
    pub async fn size(&self) -> usize {
        self.data.read().await.len()
    }

    /// Clean up expired entries
    pub async fn cleanup_expired(&self) -> usize {
        let mut data = self.data.write().await;
        let now = std::time::Instant::now();
        let initial_size = data.len();

        data.retain(|_, entry| entry.expires_at > now);

        initial_size - data.len()
    }
}

/// Cache entry with expiration
#[derive(Debug, Clone)]
struct CacheEntry<V> {
    value: V,
    expires_at: std::time::Instant,
}

// =================================================================================================
// Integration Generics - Parameterized backend integration utilities
// =================================================================================================

/// Generic integration client for backend services
pub struct GenericIntegrationClient<TClient> {
    client: Arc<TClient>,
    connection_status: Arc<RwLock<ConnectionStatus>>,
    health_checker: Option<Arc<dyn HealthChecker<TClient> + Send + Sync>>,
    retry_policy: RetryPolicy,
}

impl<TClient> GenericIntegrationClient<TClient>
where
    TClient: Send + Sync + 'static,
{
    /// Create a new generic integration client
    pub fn new(client: TClient, retry_policy: RetryPolicy) -> Self {
        Self {
            client: Arc::new(client),
            connection_status: Arc::new(RwLock::new(ConnectionStatus::NotConfigured)),
            health_checker: None,
            retry_policy,
        }
    }

    /// Set health checker
    pub fn with_health_checker(mut self, checker: Arc<dyn HealthChecker<TClient> + Send + Sync>) -> Self {
        self.health_checker = Some(checker);
        self
    }

    /// Get client reference
    pub fn client(&self) -> &Arc<TClient> {
        &self.client
    }

    /// Get connection status
    pub async fn connection_status(&self) -> ConnectionStatus {
        self.connection_status.read().await.clone()
    }

    /// Set connection status
    pub async fn set_connection_status(&self, status: ConnectionStatus) {
        *self.connection_status.write().await = status;
    }

    /// Perform health check
    pub async fn health_check(&self) -> PrismaResult<HealthStatus> {
        if let Some(ref checker) = self.health_checker {
            checker.check_health(&self.client).await
        } else {
            Ok(HealthStatus::Unknown)
        }
    }

    /// Execute operation with retry policy
    pub async fn execute_with_retry<F, R, E>(&self, operation: F) -> Result<R, E>
    where
        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, E>> + Send>> + Send + Sync,
        E: std::fmt::Display + Send + Sync,
    {
        let mut attempts = 0;
        let mut delay = self.retry_policy.initial_delay;

        loop {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    attempts += 1;
                    if attempts >= self.retry_policy.max_attempts {
                        return Err(error);
                    }

                    tracing::warn!("Operation failed (attempt {}/{}): {}", attempts, self.retry_policy.max_attempts, error);
                    tokio::time::sleep(delay).await;
                    delay = std::cmp::min(delay * 2, self.retry_policy.max_delay);
                }
            }
        }
    }
}

/// Trait for health checking integration clients
#[async_trait]
pub trait HealthChecker<T>: Send + Sync {
    async fn check_health(&self, client: &T) -> PrismaResult<HealthStatus>;
}

/// Retry policy configuration
#[derive(Debug, Clone)]
pub struct RetryPolicy {
    pub max_attempts: u32,
    pub initial_delay: std::time::Duration,
    pub max_delay: std::time::Duration,
}

impl Default for RetryPolicy {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            initial_delay: std::time::Duration::from_millis(100),
            max_delay: std::time::Duration::from_secs(5),
        }
    }
}

/// Generic connection pool for managing multiple connections
pub struct GenericConnectionPool<T> {
    connections: Arc<RwLock<Vec<Arc<T>>>>,
    max_connections: usize,
    current_index: Arc<Mutex<usize>>,
    factory: Arc<dyn ConnectionFactory<T> + Send + Sync>,
}

impl<T> GenericConnectionPool<T>
where
    T: Send + Sync + 'static,
{
    /// Create a new connection pool
    pub fn new(max_connections: usize, factory: Arc<dyn ConnectionFactory<T> + Send + Sync>) -> Self {
        Self {
            connections: Arc::new(RwLock::new(Vec::new())),
            max_connections,
            current_index: Arc::new(Mutex::new(0)),
            factory,
        }
    }

    /// Get a connection from the pool (round-robin)
    pub async fn get_connection(&self) -> PrismaResult<Arc<T>> {
        {
            let connections = self.connections.read().await;
            if !connections.is_empty() {
                let mut index = self.current_index.lock().await;
                let connection = connections[*index % connections.len()].clone();
                *index = (*index + 1) % connections.len();
                return Ok(connection);
            }
        }

        // If we reach here, connections was empty, so try to ensure connections
        self.ensure_connections().await?;

        let connections = self.connections.read().await;
        if connections.is_empty() {
            return Err(crate::err::types::PrismaError::new(
                std::io::Error::new(std::io::ErrorKind::NotConnected, "No connections available")
            ));
        }

        let mut index = self.current_index.lock().await;
        let connection = connections[*index % connections.len()].clone();
        *index = (*index + 1) % connections.len();

        Ok(connection)
    }

    /// Ensure minimum connections are available
    async fn ensure_connections(&self) -> PrismaResult<()> {
        let mut connections = self.connections.write().await;

        while connections.len() < self.max_connections {
            match self.factory.create_connection().await {
                Ok(connection) => connections.push(Arc::new(connection)),
                Err(e) => {
                    tracing::error!("Failed to create connection: {}", e);
                    break;
                }
            }
        }

        Ok(())
    }

    /// Get pool statistics
    pub async fn stats(&self) -> PoolStats {
        let connections = self.connections.read().await;
        PoolStats {
            total_connections: connections.len(),
            max_connections: self.max_connections,
            active_connections: connections.len(), // Simplified - in real implementation would track usage
        }
    }
}

/// Trait for creating connections
#[async_trait]
pub trait ConnectionFactory<T>: Send + Sync {
    async fn create_connection(&self) -> PrismaResult<T>;
}

/// Connection pool statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStats {
    pub total_connections: usize,
    pub max_connections: usize,
    pub active_connections: usize,
}

// =================================================================================================
// Utility Functions and Helpers
// =================================================================================================

/// Create a paginated response from a collection
pub fn paginate<T>(items: Vec<T>, page: u32, page_size: u32, total_count: u64) -> PaginatedCollection<T> {
    PaginatedCollection::new(items, total_count, page, page_size)
}

/// Create a successful UI response
pub fn success_response<T>(request_id: impl Into<String>, payload: T) -> UiResponse<T> {
    UiResponse::success(request_id, payload)
}

/// Create an error UI response
pub fn error_response<T>(request_id: impl Into<String>, code: impl Into<String>, message: impl Into<String>) -> UiResponse<T> {
    let error = UiError::new(code, message);
    UiResponse::error(request_id, error)
}

/// Generate a unique request ID
pub fn generate_request_id() -> String {
    use std::time::{SystemTime, UNIX_EPOCH};
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_nanos();
    format!("req_{}", timestamp)
}

/// Generate a unique session ID
pub fn generate_session_id() -> SessionId {
    use std::time::{SystemTime, UNIX_EPOCH};
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_nanos();
    format!("sess_{}", timestamp)
}

/// Validate email format
pub fn validate_email(email: &str) -> bool {
    // Simple email validation - in production, use a proper email validation library
    if email.len() < 5 || !email.contains('@') || !email.contains('.') {
        return false;
    }

    // Check for basic structure: <EMAIL>
    let parts: Vec<&str> = email.split('@').collect();
    if parts.len() != 2 {
        return false;
    }

    let local = parts[0];
    let domain = parts[1];

    // Local part should not be empty and should not start/end with dots
    if local.is_empty() || local.starts_with('.') || local.ends_with('.') {
        return false;
    }

    // Domain should contain at least one dot and not be empty
    if domain.is_empty() || !domain.contains('.') {
        return false;
    }

    // Domain should not start or end with dots or hyphens
    if domain.starts_with('.') || domain.ends_with('.') || domain.starts_with('-') || domain.ends_with('-') {
        return false;
    }

    // Check that domain has at least one part after the last dot (TLD)
    let domain_parts: Vec<&str> = domain.split('.').collect();
    if domain_parts.len() < 2 || domain_parts.last().unwrap().is_empty() {
        return false;
    }

    true
}

/// Hash password (placeholder - use proper password hashing in production)
pub fn hash_password(password: &str) -> String {
    // This is a placeholder - use bcrypt or similar in production
    format!("hashed_{}", password)
}

/// Verify password hash (placeholder)
pub fn verify_password(password: &str, hash: &str) -> bool {
    // This is a placeholder - use proper password verification in production
    hash == format!("hashed_{}", password)
}

/// Convert timestamp to human-readable format
pub fn format_timestamp(timestamp: chrono::DateTime<chrono::Utc>) -> String {
    timestamp.format("%Y-%m-%d %H:%M:%S UTC").to_string()
}

/// Calculate time difference in human-readable format
pub fn time_ago(timestamp: chrono::DateTime<chrono::Utc>) -> String {
    let now = chrono::Utc::now();
    let duration = now.signed_duration_since(timestamp);

    if duration.num_seconds() < 60 {
        "just now".to_string()
    } else if duration.num_minutes() < 60 {
        format!("{} minutes ago", duration.num_minutes())
    } else if duration.num_hours() < 24 {
        format!("{} hours ago", duration.num_hours())
    } else {
        format!("{} days ago", duration.num_days())
    }
}

/// Sanitize user input
pub fn sanitize_input(input: &str) -> String {
    // Basic sanitization - remove control characters and trim
    input.chars()
        .filter(|c| !c.is_control() || c.is_whitespace())
        .collect::<String>()
        .trim()
        .to_string()
}

/// Truncate text to specified length
pub fn truncate_text(text: &str, max_length: usize) -> String {
    if text.len() <= max_length {
        text.to_string()
    } else {
        format!("{}...", &text[..max_length.saturating_sub(3)])
    }
}