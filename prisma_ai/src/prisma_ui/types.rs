// =================================================================================================
// File: /prisma_ai/src/prisma_ui/types.rs
// =================================================================================================
// Purpose: Core data types and structures for UI operations and communication.
// This file defines the fundamental data types used throughout the prisma_ui module for
// representing UI state, user interactions, configuration data, and communication structures
// between the Flutter frontend and Rust backend.
// =================================================================================================
// Internal Dependencies:
// - publishers/types.rs: Publisher-specific types and event structures
// - services/types.rs: Service layer data types and request/response structures
// - ../prisma/types.rs: Core PRISMA system types for integration
// =================================================================================================
// External Dependencies:
// - serde: Serialization and deserialization for JSON communication
// - chrono: Date and time handling for timestamps and scheduling
// - uuid: Unique identifier generation for UI entities
// - std::collections: HashMap and other collection types for data organization
// =================================================================================================
// Module Interactions:
// - Used by all services for data structure definitions
// - Shared with publishers for event payload structures
// - Integrated with Flutter UI for JSON serialization compatibility
// - Provides type safety for UI-backend communication
// - Defines configuration structures for pm.toml integration
// =================================================================================================
// Type Categories:
// - UI State Types: Structures representing current UI state and user sessions
// - Communication Types: Request/response structures for frontend-backend communication
// - Configuration Types: Data structures for pm.toml and dynamic configuration
// - Event Types: Structures for UI events and user interactions
// - Integration Types: Types for bridging UI with core PRISMA components
// =================================================================================================

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

// =================================================================================================
// Type Aliases and Identifiers
// =================================================================================================

/// Type alias for user identifiers
pub type UserId = String;

/// Type alias for session identifiers
pub type SessionId = String;

/// Type alias for UI component identifiers
pub type ComponentId = String;

/// Type alias for conversation identifiers
pub type ConversationId = String;

/// Type alias for agent identifiers (re-exported for UI use)
pub type AgentId = String;

/// Type alias for project identifiers
pub type ProjectId = String;

// =================================================================================================
// UI State Types
// =================================================================================================

/// Represents the current state of a user session
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SessionState {
    /// Session is being initialized
    Initializing,
    /// Session is active and ready
    Active,
    /// Session is paused/idle
    Idle,
    /// Session is expired and needs renewal
    Expired,
    /// Session is terminated
    Terminated,
}

/// Represents the overall UI application state
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum UiState {
    /// UI is loading/initializing
    Loading,
    /// UI is ready for user interaction
    Ready,
    /// UI is processing a request
    Processing,
    /// UI encountered an error
    Error,
    /// UI is offline/disconnected
    Offline,
}

/// User session information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSession {
    pub session_id: SessionId,
    pub user_id: UserId,
    pub state: SessionState,
    pub created_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub device_info: DeviceInfo,
    pub permissions: Vec<Permission>,
}

/// Device information for multi-device session management
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub device_id: String,
    pub device_type: DeviceType,
    pub platform: String,
    pub app_version: String,
    pub user_agent: Option<String>,
}

/// Types of devices that can connect to the UI
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum DeviceType {
    /// Mobile device (iOS/Android)
    Mobile,
    /// Web browser
    Web,
    /// Desktop application
    Desktop,
    /// API client
    Api,
}

/// User permissions for UI operations
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Permission {
    /// Can create and manage projects
    ProjectManagement,
    /// Can interact with agents
    AgentInteraction,
    /// Can access chat functionality
    ChatAccess,
    /// Can modify system settings
    SystemSettings,
    /// Can view system monitoring
    SystemMonitoring,
    /// Can manage other users
    UserManagement,
    /// Custom permission with specific description
    Custom(String),
}

// =================================================================================================
// Communication Types
// =================================================================================================

/// Base request structure for UI-backend communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiRequest<T> {
    pub request_id: String,
    pub session_id: SessionId,
    pub timestamp: DateTime<Utc>,
    pub request_type: String,
    pub payload: T,
}

/// Base response structure for backend-UI communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiResponse<T> {
    pub request_id: String,
    pub timestamp: DateTime<Utc>,
    pub status: ResponseStatus,
    pub payload: Option<T>,
    pub error: Option<UiError>,
}

/// Response status for UI operations
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ResponseStatus {
    /// Operation completed successfully
    Success,
    /// Operation failed with error
    Error,
    /// Operation is still processing
    Processing,
    /// Operation was cancelled
    Cancelled,
}

/// UI-specific error information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiError {
    pub code: String,
    pub message: String,
    pub details: Option<HashMap<String, serde_json::Value>>,
    pub user_message: Option<String>,
    pub retry_after: Option<u64>, // seconds
}

/// WebSocket message types for real-time communication
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum WebSocketMessage {
    /// Chat message from user or agent
    ChatMessage(ChatMessageData),
    /// System notification
    SystemNotification(SystemNotificationData),
    /// Agent status update
    AgentStatusUpdate(AgentStatusData),
    /// Task progress update
    TaskProgress(TaskProgressData),
    /// Connection heartbeat
    Heartbeat,
    /// Error message
    Error(UiError),
}

/// Chat message data for WebSocket communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessageData {
    pub conversation_id: ConversationId,
    pub message_id: String,
    pub sender_id: String,
    pub sender_type: SenderType,
    pub content: String,
    pub timestamp: DateTime<Utc>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// Type of message sender
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SenderType {
    /// Human user
    User,
    /// AI agent
    Agent,
    /// System message
    System,
}

/// System notification data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemNotificationData {
    pub notification_id: String,
    pub title: String,
    pub message: String,
    pub severity: NotificationSeverity,
    pub timestamp: DateTime<Utc>,
    pub actions: Vec<NotificationAction>,
}

/// Notification severity levels
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum NotificationSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

/// Actions that can be taken on notifications
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationAction {
    pub action_id: String,
    pub label: String,
    pub action_type: ActionType,
}

/// Types of notification actions
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ActionType {
    /// Dismiss the notification
    Dismiss,
    /// Navigate to a specific UI location
    Navigate(String),
    /// Execute a specific command
    Command(String),
    /// Open external link
    ExternalLink(String),
}

/// Agent status data for real-time updates
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentStatusData {
    pub agent_id: AgentId,
    pub status: AgentStatus,
    pub current_task: Option<String>,
    pub capabilities: Vec<String>,
    pub last_activity: DateTime<Utc>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// Agent status for UI display
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum AgentStatus {
    /// Agent is initializing
    Initializing,
    /// Agent is available for tasks
    Available,
    /// Agent is currently busy
    Busy,
    /// Agent is paused
    Paused,
    /// Agent encountered an error
    Error,
    /// Agent is offline
    Offline,
}

/// Task progress data for UI updates
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskProgressData {
    pub task_id: String,
    pub agent_id: Option<AgentId>,
    pub progress: TaskProgress,
    pub status: TaskStatus,
    pub estimated_completion: Option<DateTime<Utc>>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// Task progress information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskProgress {
    pub current_step: u32,
    pub total_steps: Option<u32>,
    pub percentage: Option<f32>,
    pub description: Option<String>,
}

/// Task execution status
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum TaskStatus {
    /// Task is queued for execution
    Queued,
    /// Task is currently running
    Running,
    /// Task completed successfully
    Completed,
    /// Task failed with error
    Failed,
    /// Task was cancelled
    Cancelled,
    /// Task is paused
    Paused,
}

// =================================================================================================
// Configuration Types
// =================================================================================================

/// UI configuration loaded from pm.toml
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiConfig {
    pub server: ServerConfig,
    pub rabbitmq: RabbitMqConfig,
    pub database: DatabaseConfig,
    pub auth: AuthConfig,
    pub features: FeatureConfig,
    pub ui_settings: UiSettings,
}

/// Server configuration for UI backend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub tls_enabled: bool,
    pub cors_origins: Vec<String>,
    pub max_connections: Option<u32>,
}

/// RabbitMQ configuration for UI communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMqConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub virtual_host: Option<String>,
    pub exchange: String,
    pub queue_prefix: String,
    pub websocket_enabled: bool,
    pub websocket_port: Option<u16>,
}

/// Database configuration for UI data persistence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub host: String,
    pub port: u16,
    pub database: String,
    pub username: Option<String>,
    pub password: Option<String>,
    pub connection_pool_size: Option<u32>,
}

/// Authentication configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthConfig {
    pub jwt_secret: String,
    pub jwt_expiry_hours: u32,
    pub refresh_token_expiry_days: u32,
    pub password_min_length: u32,
    pub require_email_verification: bool,
    pub max_login_attempts: u32,
    pub lockout_duration_minutes: u32,
}

/// Feature flags and toggles
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureConfig {
    pub chat_enabled: bool,
    pub agent_management_enabled: bool,
    pub project_creation_enabled: bool,
    pub mcp_integration_enabled: bool,
    pub telemetry_enabled: bool,
    pub debug_mode: bool,
}

/// UI-specific settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiSettings {
    pub theme: String,
    pub language: String,
    pub timezone: String,
    pub date_format: String,
    pub max_chat_history: u32,
    pub auto_save_interval_seconds: u32,
    pub notification_settings: NotificationSettings,
}

/// Notification preferences
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationSettings {
    pub desktop_notifications: bool,
    pub sound_enabled: bool,
    pub email_notifications: bool,
    pub severity_filter: Vec<NotificationSeverity>,
}

// =================================================================================================
// Event Types
// =================================================================================================

/// UI events that can be triggered by user interactions
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "event_type", content = "data")]
pub enum UiEvent {
    /// User clicked on a UI element
    Click(ClickEvent),
    /// User navigated to a different page/view
    Navigation(NavigationEvent),
    /// User input in a form field
    Input(InputEvent),
    /// User submitted a form
    FormSubmit(FormSubmitEvent),
    /// User initiated a chat message
    ChatMessage(ChatEvent),
    /// User performed an agent action
    AgentAction(AgentActionEvent),
    /// User modified project settings
    ProjectAction(ProjectActionEvent),
    /// System event (not user-initiated)
    System(SystemEvent),
}

/// Click event data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClickEvent {
    pub element_id: ComponentId,
    pub element_type: String,
    pub coordinates: Option<(f32, f32)>,
    pub modifiers: Vec<KeyModifier>,
}

/// Navigation event data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NavigationEvent {
    pub from_route: String,
    pub to_route: String,
    pub navigation_type: NavigationType,
}

/// Types of navigation
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum NavigationType {
    /// User clicked a link/button
    UserInitiated,
    /// Programmatic navigation
    Programmatic,
    /// Browser back/forward
    BrowserNavigation,
    /// Redirect due to authentication
    AuthRedirect,
}

/// Input event data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InputEvent {
    pub field_id: ComponentId,
    pub field_type: String,
    pub value: String,
    pub validation_status: ValidationStatus,
}

/// Form validation status
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ValidationStatus {
    Valid,
    Invalid(Vec<String>), // List of validation errors
    Pending,
}

/// Form submission event data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormSubmitEvent {
    pub form_id: ComponentId,
    pub form_type: String,
    pub fields: HashMap<String, serde_json::Value>,
    pub validation_passed: bool,
}

/// Chat event data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatEvent {
    pub conversation_id: ConversationId,
    pub message: String,
    pub target_agent_id: Option<AgentId>,
    pub attachments: Vec<AttachmentInfo>,
}

/// File attachment information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttachmentInfo {
    pub file_name: String,
    pub file_type: String,
    pub file_size: u64,
    pub file_hash: Option<String>,
}

/// Agent action event data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentActionEvent {
    pub agent_id: AgentId,
    pub action: AgentAction,
    pub parameters: Option<HashMap<String, serde_json::Value>>,
}

/// Types of agent actions
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum AgentAction {
    /// Start the agent
    Start,
    /// Pause the agent
    Pause,
    /// Stop the agent
    Stop,
    /// Configure agent settings
    Configure,
    /// Assign a task to the agent
    AssignTask,
    /// View agent status
    ViewStatus,
}

/// Project action event data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectActionEvent {
    pub project_id: ProjectId,
    pub action: ProjectAction,
    pub parameters: Option<HashMap<String, serde_json::Value>>,
}

/// Types of project actions
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ProjectAction {
    /// Create a new project
    Create,
    /// Open an existing project
    Open,
    /// Save project changes
    Save,
    /// Delete a project
    Delete,
    /// Configure project settings
    Configure,
    /// Load model for project
    LoadModel,
}

/// System event data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemEvent {
    pub event_id: String,
    pub event_type: SystemEventType,
    pub description: String,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// Types of system events
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SystemEventType {
    /// System startup
    Startup,
    /// System shutdown
    Shutdown,
    /// Configuration change
    ConfigChange,
    /// Service status change
    ServiceStatusChange,
    /// Error occurred
    Error,
    /// Performance metric
    Performance,
}

/// Key modifiers for input events
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum KeyModifier {
    Ctrl,
    Alt,
    Shift,
    Meta, // Cmd on Mac, Windows key on PC
}

// =================================================================================================
// Integration Types
// =================================================================================================

/// Integration context for bridging UI with backend systems
pub struct IntegrationContext {
    pub prisma_engine_handle: Option<std::sync::Arc<tokio::sync::RwLock<dyn crate::prisma::prisma_engine::traits::EngineController + Send + Sync>>>,
    pub agent_manager_handle: Option<std::sync::Arc<dyn Send + Sync>>, // Will be properly typed when agent manager traits are available
    pub storage_handle: Option<std::sync::Arc<crate::storage::surrealdb::SurrealDbConnection>>, // Use concrete type instead of trait object
    pub config: UiConfig,
}

impl Clone for IntegrationContext {
    fn clone(&self) -> Self {
        Self {
            prisma_engine_handle: self.prisma_engine_handle.clone(),
            agent_manager_handle: self.agent_manager_handle.clone(),
            storage_handle: self.storage_handle.clone(),
            config: self.config.clone(),
        }
    }
}

impl std::fmt::Debug for IntegrationContext {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("IntegrationContext")
            .field("prisma_engine_handle", &self.prisma_engine_handle.is_some())
            .field("agent_manager_handle", &self.agent_manager_handle.is_some())
            .field("storage_handle", &self.storage_handle.is_some())
            .field("config", &self.config)
            .finish()
    }
}

/// Task submission request for PrismaEngine integration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskSubmissionRequest {
    pub task_type: String,
    pub parameters: HashMap<String, serde_json::Value>,
    pub priority: TaskPriority,
    pub agent_id: Option<AgentId>,
    pub conversation_id: Option<ConversationId>,
}

/// Task priority levels for UI-submitted tasks
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum TaskPriority {
    /// Real-time tasks (user interactions)
    Realtime,
    /// High priority tasks
    High,
    /// Normal priority tasks
    Normal,
    /// Low priority background tasks
    Low,
}

/// Agent registration request for Agent Manager integration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentRegistrationRequest {
    pub agent_id: AgentId,
    pub agent_config: AgentConfiguration,
    pub capabilities: Vec<String>,
    pub initial_state: AgentStatus,
}

/// Agent configuration for UI-created agents
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentConfiguration {
    pub name: String,
    pub description: Option<String>,
    pub role: String,
    pub goal: String,
    pub model_config: ModelConfiguration,
    pub prompt_template: Option<String>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
}

/// Model configuration for agents
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfiguration {
    pub model_path: String,
    pub model_type: String,
    pub context_length: Option<u32>,
    pub embedding_model: Option<String>,
}

/// Storage operation request for database integration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageRequest {
    pub operation: StorageOperation,
    pub table: String,
    pub data: Option<serde_json::Value>,
    pub query: Option<String>,
    pub filters: Option<HashMap<String, serde_json::Value>>,
}

/// Types of storage operations
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum StorageOperation {
    Create,
    Read,
    Update,
    Delete,
    Query,
}

/// Connection status for external integrations
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ConnectionStatus {
    /// Connection is healthy
    Connected,
    /// Connection is being established
    Connecting,
    /// Connection is temporarily unavailable
    Disconnected,
    /// Connection failed with error
    Error,
    /// Connection is not configured
    NotConfigured,
}

/// Health check information for integrated services
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceHealth {
    pub service_name: String,
    pub status: ConnectionStatus,
    pub last_check: DateTime<Utc>,
    pub response_time_ms: Option<u64>,
    pub error_message: Option<String>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

// =================================================================================================
// Default Implementations
// =================================================================================================

impl Default for UiState {
    fn default() -> Self {
        UiState::Loading
    }
}

impl Default for SessionState {
    fn default() -> Self {
        SessionState::Initializing
    }
}

impl Default for DeviceType {
    fn default() -> Self {
        DeviceType::Web
    }
}

impl Default for ResponseStatus {
    fn default() -> Self {
        ResponseStatus::Success
    }
}

impl Default for TaskPriority {
    fn default() -> Self {
        TaskPriority::Normal
    }
}

impl Default for AgentStatus {
    fn default() -> Self {
        AgentStatus::Initializing
    }
}

impl Default for TaskStatus {
    fn default() -> Self {
        TaskStatus::Queued
    }
}

impl Default for ConnectionStatus {
    fn default() -> Self {
        ConnectionStatus::NotConfigured
    }
}

// =================================================================================================
// Utility Functions
// =================================================================================================

impl UiError {
    /// Create a new UI error with code and message
    pub fn new(code: impl Into<String>, message: impl Into<String>) -> Self {
        Self {
            code: code.into(),
            message: message.into(),
            details: None,
            user_message: None,
            retry_after: None,
        }
    }

    /// Create a UI error with user-friendly message
    pub fn with_user_message(mut self, user_message: impl Into<String>) -> Self {
        self.user_message = Some(user_message.into());
        self
    }

    /// Add retry delay to the error
    pub fn with_retry_after(mut self, seconds: u64) -> Self {
        self.retry_after = Some(seconds);
        self
    }
}

impl<T> UiResponse<T> {
    /// Create a successful response
    pub fn success(request_id: impl Into<String>, payload: T) -> Self {
        Self {
            request_id: request_id.into(),
            timestamp: Utc::now(),
            status: ResponseStatus::Success,
            payload: Some(payload),
            error: None,
        }
    }

    /// Create an error response
    pub fn error(request_id: impl Into<String>, error: UiError) -> Self {
        Self {
            request_id: request_id.into(),
            timestamp: Utc::now(),
            status: ResponseStatus::Error,
            payload: None,
            error: Some(error),
        }
    }
}